@echo off
echo 🔥 Firestore Index Deployment Script
echo =====================================
echo.

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI is not installed. Please install it first:
    echo npm install -g firebase-tools
    pause
    exit /b 1
)

echo ✅ Firebase CLI is installed

REM Check if firestore.indexes.json exists
if not exist "firestore.indexes.json" (
    echo ❌ firestore.indexes.json not found
    pause
    exit /b 1
)

echo ✅ firestore.indexes.json found

echo.
echo 🚀 Deploying indexes...
firebase deploy --only firestore:indexes

if %errorlevel% equ 0 (
    echo.
    echo ✅ Indexes deployed successfully!
    echo.
    echo 📝 Note: It may take several minutes for indexes to build.
    echo    You can check the status in the Firebase Console:
    echo    https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/indexes
) else (
    echo.
    echo ❌ Failed to deploy indexes
    echo.
    echo 🔧 Manual deployment steps:
    echo 1. Run: firebase login
    echo 2. Run: firebase use YOUR_PROJECT_ID
    echo 3. Run: firebase deploy --only firestore:indexes
)

echo.
echo 🎉 Deployment complete!
pause
