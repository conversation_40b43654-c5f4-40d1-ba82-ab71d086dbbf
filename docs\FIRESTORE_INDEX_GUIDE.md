# Firestore Index Management Guide

This guide explains how to manage Firestore indexes for the Masalit AI platform and resolve index-related errors.

## 🔥 Understanding the Error

When you see an error like:
```
FirebaseError: The query requires an index. You can create it here: https://console.firebase.google.com/...
```

This means Firestore needs a composite index for queries that combine:
- Multiple `where` clauses
- `where` + `orderBy` clauses
- Complex filtering operations

## 🚀 Quick Fix Solutions

### Option 1: Automatic Index Creation (Recommended)

1. **Deploy the provided indexes:**
   ```bash
   # Make the script executable
   chmod +x scripts/deploy-firestore-indexes.js
   
   # Run the deployment script
   node scripts/deploy-firestore-indexes.js
   ```

2. **Or manually deploy:**
   ```bash
   firebase deploy --only firestore:indexes
   ```

### Option 2: Manual Index Creation

1. Click the URL in the error message
2. This will take you to Firebase Console
3. Click "Create Index"
4. Wait for the index to build (can take several minutes)

### Option 3: Code Modifications (Already Implemented)

We've modified the problematic queries to avoid index requirements:
- Removed `orderBy` from queries with `where` clauses
- Added client-side sorting instead
- Increased query limits to accommodate filtering

## 📋 Required Indexes

The following indexes are defined in `firestore.indexes.json`:

### Users Collection
```json
{
  "collectionGroup": "users",
  "fields": [
    {"fieldPath": "role", "order": "ASCENDING"},
    {"fieldPath": "created_at", "order": "DESCENDING"}
  ]
}
```

### Audio Collection
```json
{
  "collectionGroup": "audio",
  "fields": [
    {"fieldPath": "user_id", "order": "ASCENDING"},
    {"fieldPath": "created_at", "order": "DESCENDING"}
  ]
}
```

### Security Collections
```json
{
  "collectionGroup": "user_sessions",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "login_time", "order": "DESCENDING"}
  ]
}
```

## 🛠️ Troubleshooting

### Error: "Firebase CLI not found"
```bash
npm install -g firebase-tools
firebase login
```

### Error: "Project not configured"
```bash
firebase use YOUR_PROJECT_ID
```

### Error: "Permission denied"
Make sure you have the following roles in Firebase:
- Firebase Admin
- Cloud Firestore Admin

### Index Building Takes Too Long
- Small collections: 1-5 minutes
- Large collections: 10-30 minutes
- Very large collections: 1+ hours

## 🔧 Code Patterns to Avoid Index Issues

### ❌ Bad (Requires Index)
```javascript
const query = query(
  collection(db, 'users'),
  where('role', '==', 'admin'),
  orderBy('created_at', 'desc')
)
```

### ✅ Good (No Index Required)
```javascript
// Get data first
const query = query(
  collection(db, 'users'),
  where('role', '==', 'admin')
)
const snapshot = await getDocs(query)

// Sort client-side
const users = snapshot.docs
  .map(doc => ({ id: doc.id, ...doc.data() }))
  .sort((a, b) => b.created_at - a.created_at)
```

## 📊 Index Status Monitoring

Check index status in Firebase Console:
1. Go to Firestore Database
2. Click "Indexes" tab
3. Monitor build progress

Index states:
- **Building**: Index is being created
- **Ready**: Index is available for use
- **Error**: Index creation failed

## 🚨 Emergency Fixes

If you need immediate functionality while indexes build:

### 1. Disable Problematic Queries
```javascript
// Temporarily disable complex queries
const ENABLE_COMPLEX_QUERIES = false

if (ENABLE_COMPLEX_QUERIES) {
  // Complex query here
} else {
  // Simple fallback query
}
```

### 2. Use Simple Queries
```javascript
// Instead of complex filtering, get all and filter client-side
const allDocs = await getDocs(collection(db, 'collection'))
const filtered = allDocs.docs
  .map(doc => ({ id: doc.id, ...doc.data() }))
  .filter(item => /* your filter logic */)
  .sort((a, b) => /* your sort logic */)
```

## 📝 Best Practices

1. **Plan Indexes Early**: Define indexes before deploying queries
2. **Use Client-Side Filtering**: For small datasets, filter in JavaScript
3. **Limit Query Complexity**: Avoid multiple where + orderBy combinations
4. **Monitor Index Usage**: Check Firebase Console for unused indexes
5. **Test Locally**: Use Firebase Emulator for development

## 🔄 Maintenance

### Regular Index Cleanup
```bash
# List all indexes
firebase firestore:indexes

# Remove unused indexes (be careful!)
firebase firestore:indexes:delete
```

### Performance Monitoring
- Monitor query performance in Firebase Console
- Check for slow queries
- Optimize based on usage patterns

## 📞 Support

If you continue having index issues:
1. Check the Firebase Console for specific error messages
2. Verify your Firebase project permissions
3. Ensure you're using the correct project ID
4. Contact Firebase Support for complex index issues
