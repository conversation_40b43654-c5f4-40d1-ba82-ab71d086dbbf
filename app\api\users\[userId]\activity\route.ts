import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { auth } from '@/lib/firebase-admin'
import { collection, query, where, getDocs, orderBy, limit, addDoc, doc, getDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { listUserAudio } from '@/lib/firebase-service'

interface ActivityEvent {
  id?: string
  userId: string
  type: 'login' | 'logout' | 'audio_upload' | 'audio_approved' | 'audio_rejected' | 'profile_update' | 'password_change'
  description: string
  metadata?: any
  ip_address?: string
  user_agent?: string
  timestamp: string
}

// GET /api/users/[userId]/activity - Get user activity history
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const currentUserId = decodedClaims.uid

    // Get current user data to check role
    const currentUserDoc = await getDoc(doc(db, 'users', currentUserId))
    if (!currentUserDoc.exists()) {
      return NextResponse.json({ error: 'Current user not found' }, { status: 404 })
    }
    const currentUserData = currentUserDoc.data()

    const { userId } = await params

    // Users can view their own activity, admins can view any activity
    if (currentUserId !== userId && currentUserData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const activityType = searchParams.get('type')
    const limitParam = parseInt(searchParams.get('limit') || '50')
    const pageParam = parseInt(searchParams.get('page') || '1')

    // Get user's audio recordings as activity events
    const userAudio = await listUserAudio(userId, { limit: 1000 })
    
    // Convert audio uploads to activity events
    const audioActivities: ActivityEvent[] = userAudio.map(audio => ({
      id: audio.id,
      userId,
      type: 'audio_upload',
      description: `Uploaded audio: ${audio.transcriptions?.primary?.text?.slice(0, 50) || 'Untitled'}...`,
      metadata: {
        audioId: audio.id,
        duration: audio.duration,
        status: audio.review?.status?.action || 'pending',
        filename: audio.filename
      },
      timestamp: audio.created_at || new Date().toISOString()
    }))

    // Add approval/rejection events
    const reviewActivities: ActivityEvent[] = userAudio
      .filter(audio => audio.review?.status?.action && audio.review?.status?.action !== 'pending')
      .map(audio => ({
        id: `${audio.id}_review`,
        userId,
        type: audio.review?.status?.action === 'approved' ? 'audio_approved' : 'audio_rejected',
        description: `Audio ${audio.review?.status?.action}: ${audio.transcriptions?.primary?.text?.slice(0, 50) || 'Untitled'}...`,
        metadata: {
          audioId: audio.id,
          reviewedBy: audio.review?.status?.reviewed_by,
          reviewComment: audio.review?.status?.comment,
          reviewedAt: audio.review?.status?.reviewed_at
        },
        timestamp: audio.review?.status?.reviewed_at || audio.created_at || new Date().toISOString()
      }))

    // TODO: Add other activity types from a dedicated activity log collection
    // For now, we'll simulate some activities
    const systemActivities: ActivityEvent[] = []

    // Combine all activities
    let allActivities = [...audioActivities, ...reviewActivities, ...systemActivities]

    // Filter by type if specified
    if (activityType) {
      allActivities = allActivities.filter(activity => activity.type === activityType)
    }

    // Sort by timestamp (newest first)
    allActivities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    // Apply pagination
    const startIndex = (pageParam - 1) * limitParam
    const endIndex = startIndex + limitParam
    const paginatedActivities = allActivities.slice(startIndex, endIndex)

    // Calculate summary statistics
    const summary = {
      totalActivities: allActivities.length,
      recentActivities: allActivities.filter(a => {
        const activityDate = new Date(a.timestamp)
        const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        return activityDate >= sevenDaysAgo
      }).length,
      activityTypes: allActivities.reduce((acc, activity) => {
        acc[activity.type] = (acc[activity.type] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      lastActivity: allActivities.length > 0 ? allActivities[0].timestamp : null
    }

    return NextResponse.json({
      activities: paginatedActivities,
      summary,
      pagination: {
        page: pageParam,
        limit: limitParam,
        total: allActivities.length,
        hasMore: endIndex < allActivities.length
      }
    })
  } catch (error) {
    console.error('Error fetching user activity:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/users/[userId]/activity - Log new activity event
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const currentUserId = decodedClaims.uid

    // Get current user data to check role
    const currentUserDoc = await getDoc(doc(db, 'users', currentUserId))
    if (!currentUserDoc.exists()) {
      return NextResponse.json({ error: 'Current user not found' }, { status: 404 })
    }
    const currentUserData = currentUserDoc.data()

    const { userId } = await params
    const { type, description, metadata } = await request.json()

    // Only allow users to log their own activities or admins to log any
    if (currentUserId !== userId && currentUserData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Validate required fields
    if (!type || !description) {
      return NextResponse.json({ 
        error: 'Type and description are required' 
      }, { status: 400 })
    }

    // Get client information
    const ip_address = request.headers.get('x-forwarded-for') || 
                      request.headers.get('x-real-ip') || 
                      'unknown'
    const user_agent = request.headers.get('user-agent') || 'unknown'

    // Create activity event
    const activityEvent: ActivityEvent = {
      userId,
      type,
      description,
      metadata: metadata || {},
      ip_address,
      user_agent,
      timestamp: new Date().toISOString()
    }

    // TODO: Store in dedicated activity log collection
    // For now, we'll just return success
    // const activityRef = await addDoc(collection(db, 'activity_logs'), activityEvent)

    return NextResponse.json({
      message: 'Activity logged successfully',
      activityId: 'temp_id', // activityRef.id when implemented
      activity: activityEvent
    })
  } catch (error) {
    console.error('Error logging activity:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
