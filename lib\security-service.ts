import { collection, addDoc, query, where, orderBy, limit, getDocs, updateDoc, doc } from 'firebase/firestore'
import { db } from './firebase'

export interface LoginSession {
  userId: string
  session_id: string
  ip_address: string
  user_agent: string
  device_info: {
    browser: string
    os: string
    device_type: 'desktop' | 'mobile' | 'tablet'
    is_trusted: boolean
  }
  location?: {
    country: string
    city: string
    region: string
  }
  login_time: string
  last_activity: string
  is_active: boolean
  logout_time?: string
  logout_reason?: 'manual' | 'timeout' | 'security' | 'admin'
}

export interface SecurityEvent {
  userId: string
  event_type: 'login_success' | 'login_failed' | 'password_change' | 'suspicious_activity' | 'device_added' | 'device_removed' | 'security_settings_changed' | 'unauthorized_access'
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  ip_address: string
  user_agent: string
  metadata: any
  timestamp: string
  resolved: boolean
  resolved_by?: string
  resolved_at?: string
}

export class SecurityService {
  private static instance: SecurityService

  private constructor() {}

  public static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService()
    }
    return SecurityService.instance
  }

  // Session Management
  public async createSession(sessionData: Omit<LoginSession, 'login_time' | 'last_activity' | 'is_active'>): Promise<string> {
    try {
      const session: LoginSession = {
        ...sessionData,
        login_time: new Date().toISOString(),
        last_activity: new Date().toISOString(),
        is_active: true
      }

      const sessionRef = await addDoc(collection(db, 'users', sessionData.userId, 'sessions'), session)
      return sessionRef.id
    } catch (error) {
      console.error('Failed to create session:', error)
      throw error
    }
  }

  public async updateSessionActivity(userId: string, sessionId: string): Promise<void> {
    try {
      const sessionRef = doc(db, 'users', userId, 'sessions', sessionId)
      await updateDoc(sessionRef, {
        last_activity: new Date().toISOString()
      })
    } catch (error) {
      console.error('Failed to update session activity:', error)
    }
  }

  public async endSession(userId: string, sessionId: string, reason: LoginSession['logout_reason'] = 'manual'): Promise<void> {
    try {
      const sessionRef = doc(db, 'users', userId, 'sessions', sessionId)
      await updateDoc(sessionRef, {
        is_active: false,
        logout_time: new Date().toISOString(),
        logout_reason: reason
      })
    } catch (error) {
      console.error('Failed to end session:', error)
      throw error
    }
  }

  public async getUserSessions(userId: string, activeOnly: boolean = false): Promise<LoginSession[]> {
    try {
      // Query user's sessions subcollection
      const sessionQuery = query(
        collection(db, 'users', userId, 'sessions'),
        limit(50)
      )

      const querySnapshot = await getDocs(sessionQuery)
      let sessions = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as (LoginSession & { id: string })[]

      // Filter and sort client-side
      if (activeOnly) {
        sessions = sessions.filter(session => session.is_active === true)
      }

      // Sort by login_time descending (client-side)
      sessions.sort((a, b) => {
        const timeA = new Date(a.login_time || 0).getTime()
        const timeB = new Date(b.login_time || 0).getTime()
        return timeB - timeA
      })

      return sessions
    } catch (error) {
      console.error('Failed to get user sessions:', error)
      // Return empty array if collection doesn't exist or other errors
      return []
    }
  }

  // Security Events
  public async createSecurityEvent(eventData: Omit<SecurityEvent, 'timestamp' | 'resolved'>): Promise<string> {
    try {
      const event: SecurityEvent = {
        ...eventData,
        timestamp: new Date().toISOString(),
        resolved: false
      }

      const eventRef = await addDoc(collection(db, 'users', eventData.userId, 'security_events'), event)
      
      // Auto-resolve low severity events
      if (eventData.severity === 'low') {
        await this.resolveSecurityEvent(eventRef.id, 'system')
      }

      return eventRef.id
    } catch (error) {
      console.error('Failed to create security event:', error)
      throw error
    }
  }

  public async resolveSecurityEvent(userId: string, eventId: string, resolvedBy: string): Promise<void> {
    try {
      const eventRef = doc(db, 'users', userId, 'security_events', eventId)
      await updateDoc(eventRef, {
        resolved: true,
        resolved_by: resolvedBy,
        resolved_at: new Date().toISOString()
      })
    } catch (error) {
      console.error('Failed to resolve security event:', error)
      throw error
    }
  }

  public async getUserSecurityEvents(userId: string, unresolvedOnly: boolean = false): Promise<SecurityEvent[]> {
    try {
      // Query user's security events subcollection
      const eventQuery = query(
        collection(db, 'users', userId, 'security_events'),
        limit(100)
      )

      const querySnapshot = await getDocs(eventQuery)
      let events = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as (SecurityEvent & { id: string })[]

      // Filter and sort client-side
      if (unresolvedOnly) {
        events = events.filter(event => event.resolved === false)
      }

      // Sort by timestamp descending (client-side)
      events.sort((a, b) => {
        const timeA = new Date(a.timestamp || 0).getTime()
        const timeB = new Date(b.timestamp || 0).getTime()
        return timeB - timeA
      })

      return events
    } catch (error) {
      console.error('Failed to get security events:', error)
      // Return empty array if collection doesn't exist or other errors
      return []
    }
  }

  // Device and Browser Detection
  public parseUserAgent(userAgent: string): { browser: string; os: string; device_type: 'desktop' | 'mobile' | 'tablet' } {
    const ua = userAgent.toLowerCase()
    
    // Detect browser
    let browser = 'Unknown'
    if (ua.includes('chrome')) browser = 'Chrome'
    else if (ua.includes('firefox')) browser = 'Firefox'
    else if (ua.includes('safari')) browser = 'Safari'
    else if (ua.includes('edge')) browser = 'Edge'
    else if (ua.includes('opera')) browser = 'Opera'

    // Detect OS
    let os = 'Unknown'
    if (ua.includes('windows')) os = 'Windows'
    else if (ua.includes('mac')) os = 'macOS'
    else if (ua.includes('linux')) os = 'Linux'
    else if (ua.includes('android')) os = 'Android'
    else if (ua.includes('ios')) os = 'iOS'

    // Detect device type
    let device_type: 'desktop' | 'mobile' | 'tablet' = 'desktop'
    if (ua.includes('mobile')) device_type = 'mobile'
    else if (ua.includes('tablet') || ua.includes('ipad')) device_type = 'tablet'

    return { browser, os, device_type }
  }

  // Security Analysis
  public async analyzeLoginAttempt(
    userId: string,
    ip_address: string,
    user_agent: string,
    success: boolean
  ): Promise<{ suspicious: boolean; reasons: string[] }> {
    try {
      const reasons: string[] = []
      let suspicious = false

      // Get recent sessions for this user (with fallback)
      let recentSessions: LoginSession[] = []
      try {
        recentSessions = await this.getUserSessions(userId)
      } catch (sessionError) {
        console.warn('Could not fetch user sessions for security analysis:', sessionError)
        // Continue with empty sessions array
      }

      // Check for unusual IP address (only if we have session history)
      if (recentSessions.length > 0) {
        const recentIPs = recentSessions
          .filter(session => new Date(session.login_time) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)) // Last 30 days
          .map(session => session.ip_address)

        if (!recentIPs.includes(ip_address) && recentIPs.length > 0) {
          suspicious = true
          reasons.push('Login from new IP address')
        }

        // Check for unusual device/browser
        const deviceInfo = this.parseUserAgent(user_agent)
        const recentDevices = recentSessions
          .filter(session => new Date(session.login_time) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) // Last 7 days
          .filter(session => session.device_info) // Ensure device_info exists
          .map(session => `${session.device_info.browser}-${session.device_info.os}`)

        const currentDevice = `${deviceInfo.browser}-${deviceInfo.os}`
        if (!recentDevices.includes(currentDevice) && recentDevices.length > 0) {
          suspicious = true
          reasons.push('Login from new device/browser')
        }
      }

      // Check for rapid login attempts (potential brute force) with fallback
      try {
        const recentFailedAttempts = await this.getUserSecurityEvents(userId)
        const failedAttempts = recentFailedAttempts.filter(event =>
          event.event_type === 'login_failed' &&
          new Date(event.timestamp) > new Date(Date.now() - 15 * 60 * 1000) // Last 15 minutes
        )

        if (failedAttempts.length >= 3) {
          suspicious = true
          reasons.push('Multiple failed login attempts detected')
        }
      } catch (eventsError) {
        console.warn('Could not fetch security events for brute force analysis:', eventsError)
        // Continue without this check
      }

      // Log the login attempt (with fallback)
      try {
        await this.createSecurityEvent({
          userId,
          event_type: success ? 'login_success' : 'login_failed',
          description: success
            ? `Successful login from ${ip_address}`
            : `Failed login attempt from ${ip_address}`,
          severity: suspicious ? 'medium' : 'low',
          ip_address,
          user_agent,
          metadata: {
            suspicious,
            reasons,
            device_info: this.parseUserAgent(user_agent)
          }
        })
      } catch (logError) {
        console.warn('Could not log security event:', logError)
        // Continue without logging
      }

      return { suspicious, reasons }
    } catch (error) {
      console.error('Failed to analyze login attempt:', error)
      return { suspicious: false, reasons: [] }
    }
  }

  // Convenience methods for common security events
  public async trackPasswordChange(userId: string, ip_address: string, user_agent: string): Promise<void> {
    await this.createSecurityEvent({
      userId,
      event_type: 'password_change',
      description: 'Password changed successfully',
      severity: 'medium',
      ip_address,
      user_agent,
      metadata: {}
    })
  }

  public async trackSuspiciousActivity(
    userId: string,
    description: string,
    ip_address: string,
    user_agent: string,
    metadata: any = {}
  ): Promise<void> {
    await this.createSecurityEvent({
      userId,
      event_type: 'suspicious_activity',
      description,
      severity: 'high',
      ip_address,
      user_agent,
      metadata
    })
  }

  public async trackUnauthorizedAccess(
    userId: string,
    resource: string,
    ip_address: string,
    user_agent: string
  ): Promise<void> {
    await this.createSecurityEvent({
      userId,
      event_type: 'unauthorized_access',
      description: `Unauthorized access attempt to ${resource}`,
      severity: 'critical',
      ip_address,
      user_agent,
      metadata: { resource }
    })
  }
}

// Export singleton instance
export const securityService = SecurityService.getInstance()

// Hook for React components
export function useSecurityService() {
  return securityService
}
