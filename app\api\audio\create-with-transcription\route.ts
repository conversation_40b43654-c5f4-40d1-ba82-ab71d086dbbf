import { NextRequest, NextResponse } from 'next/server'
import { doc, setDoc, collection } from 'firebase/firestore'
import { db } from '@/lib/firebase'

export async function POST(request: NextRequest) {
  try {
    const audioData = await request.json()
    
    // Validate required fields
    const requiredFields = ['title', 'duration', 'format', 'user_id', 'source', 'audio_url', 'transcription', 'metadata']
    for (const field of requiredFields) {
      if (!audioData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Generate audio ID
    const audio_id = `audio_${Date.now()}`
    const timestamp = new Date().toISOString()

    // Create main audio document
    const audioRef = doc(db, 'audio', audio_id)
    await setDoc(audioRef, {
      id: audio_id,
      title: audioData.title,
      audio_url: audioData.audio_url,
      duration: audioData.duration,
      format: audioData.format,
      created_at: timestamp,
      updated_at: timestamp,
      user_id: audioData.user_id,
      source: audioData.source
    })

    // Create transcription subcollection
    const transcriptionRef = doc(db, 'audio', audio_id, 'transcriptions', 'primary')
    await setDoc(transcriptionRef, {
      content: audioData.transcription.content,
      language: audioData.transcription.language || 'masalit',
      transcription_source: audioData.transcription.transcription_source || 'human/manual',
      type: audioData.transcription.type || 'txt',
      created_at: timestamp,
      updated_at: timestamp,
      speaker_count: 1
    })

    // Create metadata subcollection
    const metadataRef = doc(db, 'audio', audio_id, 'metadata', 'details')
    await setDoc(metadataRef, {
      gender: audioData.metadata.gender,
      language: audioData.metadata.language || 'masalit',
      recording_context: audioData.metadata.recording_context || 'direct_upload',
      created_at: timestamp,
      updated_at: timestamp
    })

    // Create review subcollection with initial status
    const reviewRef = doc(db, 'audio', audio_id, 'review', 'status')
    await setDoc(reviewRef, {
      action: 'pending',
      is_flagged: false,
      created_at: timestamp,
      updated_at: timestamp
    })

    // Create training subcollection with initial status
    const trainingRef = doc(db, 'audio', audio_id, 'training', 'status')
    await setDoc(trainingRef, {
      trained_asr: false,
      tts_trained: false,
      training_sessions: [],
      created_at: timestamp,
      updated_at: timestamp
    })

    return NextResponse.json({
      success: true,
      audio_id: audio_id,
      message: 'Audio record created successfully with hierarchical structure'
    })

  } catch (error) {
    console.error('Error creating audio with transcription:', error)
    return NextResponse.json(
      { error: 'Failed to create audio record' },
      { status: 500 }
    )
  }
}
