import { NextResponse } from 'next/server'
import { doc, setDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'

export async function POST(request: Request) {
  try {
    // Get settings from request body
    const settings = await request.json()

    // Note: Settings and status are now handled by the backend using clean Firebase structure
    // The backend will update settings to 'settings/asr_training' and create training sessions
    // in 'training/{session_id}' with progress tracking in subcollections

    try {
      // Make request to backend service using environment variable
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL
      if (!backendUrl) {
        throw new Error('Backend URL not configured')
      }

      const response = await fetch(`${backendUrl}/api/asr/training/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to start training'
        try {
          const errorData = await response.json()
          errorMessage = errorData.detail || errorData.error || errorMessage
        } catch (e) {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage
        }
        throw new Error(errorMessage)
      }

      const data = await response.json()
      return NextResponse.json({ 
        message: 'Training started successfully',
        status: 'started',
        data
      })
    } catch (error: any) {
      // Update status to error
      await setDoc(statusRef, {
        status: 'error',
        error: error.message,
        end_time: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      throw error
    }

  } catch (error: any) {
    console.error('Error starting training:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to start training' },
      { status: 500 }
    )
  }
} 