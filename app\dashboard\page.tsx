"use client"

import { useAuth } from "@/components/auth-provider"
import { FocusedLanguageProvider } from "@/components/focused-language-provider"
import { FocusedLanguageSelector } from "@/components/focused-language-selector"
import { UserDashboard } from "@/components/dashboard/user-dashboard"
import { AdminDashboard } from "@/components/dashboard/admin-dashboard"
import { Loader2 } from "lucide-react"

function DashboardContent() {
  const { user, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Please log in to access the dashboard</h2>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Language Selector */}
      <div className="absolute top-4 right-4 z-50">
        <FocusedLanguageSelector showText={true} />
      </div>

      <div className="container mx-auto px-4 py-8">
        {user.role === 'admin' ? <AdminDashboard /> : <UserDashboard />}
      </div>
    </div>
  )
}

export default function Dashboard() {
  return (
    <FocusedLanguageProvider>
      <DashboardContent />
    </FocusedLanguageProvider>
  )
}
